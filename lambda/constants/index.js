const DYNAMO_TABLES = {
  CONTACT_RESPONSE: process.env.CONTACT_RESPONSE_DYNAMODB_TABLE_NAME,
  OPPORTUNITY_RESPONSE: process.env.OPPORTUNITY_RESPONSE_DYNAMODB_TABLE_NAME,
};

/**
 * Form Types
 */
const FORM_TYPES = {
  EMAIL_ONLY: "email",
  STAY_UP_TO_DATE: "stayUpToDate",
  BUSINESS_OPPORTUNITY: "businessOpportunity",
  EMPLOYMENT_OPPORTUNITY: "employmentOpportunity",
  CONTACT_US: "getInTouch",
  MY_T8: "myT8",
};

const JFKT8_EMAIL_TEMPLATES = {
  BUSINESS_OPPORTUNITIES_GENERAL: "JFKT8_BUSINESS_OPPORTUNITIES_GENERAL",
  BUSINESS_OPPORTUNITIES_LEASING: "JFKT8_BUSINESS_OPPORTUNITIES_LEASING",
  EMPLOYMENT_OPPORTUNITIES: "JFKT8_EMPLOYMENT_OPPORTUNITIES",
  GENERAL_CONTACT: "JFKT8_GENERAL_CONTACT",
  MYT8_CONCESSION: "JFKT8_MYT8_CONCESSION",
  GENERAL_NEWS: "JFKT8_GENERAL_NEWS",
};

/**
 * CSV file fields configs
 */

const CONTACT_US_FILE = {
  fileName: "contact-us",
  fields: [
    { id: "createdAt", title: "Date" }, // Don't change the order of this field
    { id: "propertyId", title: "Airport" },
    { id: "firstName", title: "Firstname" },
    { id: "lastName", title: "Lastname" },
    { id: "email", title: "Email" },
    { id: "mobile", title: "Mobile" },
    { id: "inquiryType", title: "Inquiry Type" },
    { id: "company", title: "Company" },
    { id: "message", title: "Message" },
    { id: "locale", title: "Locale" },
  ],
  form: FORM_TYPES.CONTACT_US,
  email: process.env.MARKETING_EMAIL,
  filter: (r) => r["inquiryType"] == "General Inquiry",
  subject: "JFKT8 - Contact us inquiries",
};

const STAY_UP_TO_DATE_FILE = {
  fileName: "stay-up-to-date",
  fields: [
    { id: "createdAt", title: "Date" }, // Don't change the order of this field
    { id: "responseId", title: "ResponseId" },
    { id: "propertyId", title: "Airport" },
    { id: "email", title: "Email" },
    { id: "mobile", title: "Mobile" },
    { id: "zipCode", title: "ZipCode" },
    // { id: "interests", title: "Interests" },
    {
      id: "stayuptodate/businessOpportunities",
      title: "Business Opportunities",
    },
    {
      id: "stayuptodate/employmentOpportunities",
      title: "Employment Opportunities",
    },
    { id: "stayuptodate/general", title: "General" },
    { id: "advancedNetworkNews", title: "Advanced Network News" },
    { id: "emailUpdates", title: "Email Updates" },
    { id: "textUpdates", title: "Text Updates" },
    { id: "termsAndConditions", title: "Terms And Conditions" },
    { id: "locale", title: "Locale" },
  ],
  form: FORM_TYPES.STAY_UP_TO_DATE,
  email: process.env.MARKETING_EMAIL,
  subject: "Stay in touch inquiries",
};

const BUSINESS_OPPORTUNITY_FILE = {
  fileName: "business-inquiries",
  fields: [
    { id: "createdAt", title: "Date" }, // Don't change the order of this field
    { id: "responseId", title: "ResponseId" },
    { id: "propertyId", title: "Airport" },
    { id: "firstName", title: "Firstname" },
    { id: "lastName", title: "Lastname" },
    { id: "email", title: "Email" },
    { id: "mobile", title: "Mobile" },
    { id: "companyName", title: "Company Name" },
    { id: "businessAddress1", title: "Business Address 1" },
    { id: "businessAddress2", title: "Business Address 2" },
    { id: "city", title: "City" },
    { id: "state", title: "State" },
    { id: "zipCode", title: "ZipCode" },
    { id: "website", title: "Website" },
    // { id: 'businessInterests', title: 'Business Interests' },
    {
      id: "bi/architectureDesignEngineering",
      title: "Architecture/Design Engineering",
    },
    { id: "bi/construction", title: "Construction" },
    { id: "bi/foodAndBeverage", title: "Food & Beverage" },
    { id: "bi/productManufacturer", title: "Product Manufacturer/Maker" },
    { id: "bi/professionalServices", title: "Professional Services" },
    {
      id: "bi/retailBusinessBrickMortar",
      title: "Retail Business (Brick & Mortar)",
    },
    { id: "bi/retailBusinessOnline", title: "Retail Business (Online)" },
    { id: "bi/supplier", title: "Supplier" },
    { id: "bi/other", title: "Other" },
    { id: "aboutBusiness", title: "About Business" },
    { id: "yearsInBusiness", title: "Years In Business" },
    // { id: 'certification', title: 'Certification' },
    {
      id: "cert/acdbe",
      title: "ACDBE - Airport Concessions Disadvantaged Business Enterprise",
    },
    { id: "cert/lbe", title: "LBE - Local Business Enterprise" },
    { id: "cert/mbe", title: "MBE - Minority Business Enterprise" },
    {
      id: "cert/sdvosb",
      title: "SDVOSB - Service-Disabled Veteran-Owned Small Businesses",
    },
    { id: "cert/wbe", title: "WBE - Women Business Enterprise" },
    { id: "cert/sbe", title: "SBE - Small Business Enterprise" },
    { id: "cert/notCertified", title: "Not Certified" },
    { id: "cert/other", title: "Other" },
    { id: "airportExperience", title: "Airport Experience" },
    { id: "advancedNetworkNews", title: "Advanced Network News" },
    { id: "emailUpdates", title: "Email Updates" },
    { id: "textUpdates", title: "Text Updates" },
    { id: "termsAndConditions", title: "Terms And Conditions" },
    { id: "locale", title: "Locale" },

    // { id: 'createdAt', title: 'Date' },
  ],
  //   filter: "ALL",
  //   categoryLength: 8,
  // filter: (r) =>
  //   r["bi/architectureDesignEngineering"] &&
  //   r["bi/construction"] &&
  //   r["bi/foodAndBeverage"] &&
  //   r["bi/productManufacturer"] &&
  //   r["bi/retailBusinessBrickMortar"] &&
  //   r["bi/retailBusinessOnline"] &&
  //   r["bi/supplier"] &&
  //   r["bi/other"],
  form: FORM_TYPES.BUSINESS_OPPORTUNITY,
  email: process.env.MARKETING_EMAIL,
  subject: "JFKT8 - Business Opportunities",
};

const BUSINESS_OPPORTUNITY_FILE_HOLT = {
  fileName: "business-inquiries-holt",
  fields: BUSINESS_OPPORTUNITY_FILE.fields,
  //   filter: ["Architecture/Design Engineering", "Construction"],
  filter: (r) => r["bi/architectureDesignEngineering"] || r["bi/construction"],
  form: FORM_TYPES.BUSINESS_OPPORTUNITY,
  email: process.env.HOLT_EMAIL,
  subject: "Business Opportunities",
};

const BUSINESS_OPPORTUNITY_FILE_LEASING = {
  fileName: "business-inquiries-leasing",
  fields: BUSINESS_OPPORTUNITY_FILE.fields,
  //   filter: [
  //     "Food & Beverage",
  //     "Product Manufacturer/Maker",
  //     "Retail Business (Brick & Mortar)",
  //     "Retail Business (Online)",
  //   ],
  filter: (r) => r["bi/foodAndBeverage"] || r["bi/productManufacturer"] || r["bi/retailBusinessBrickMortar"] || r["bi/retailBusinessOnline"],
  form: FORM_TYPES.BUSINESS_OPPORTUNITY,
  email: process.env.LEASING_EMAIL,
  subject: "Business Opportunities",
};

const BUSINESS_OPPORTUNITY_FILE_OPS = {
  fileName: "business-inquiries-ops",
  fields: BUSINESS_OPPORTUNITY_FILE.fields,
  //   filter: ["Supplier", "Other"],
  filter: (r) => r["bi/supplier"] || r["bi/other"],
  form: FORM_TYPES.BUSINESS_OPPORTUNITY,
  email: process.env.OPS_EMAIL,
  subject: "Business Opportunities",
};

const EMPLOYMENT_OPPORTUNITY_FILE = {
  fileName: "employment-inquiries",
  fields: [
    { id: "createdAt", title: "Date" }, // Don't change the order of this field
    { id: "responseId", title: "ResponseId" },
    { id: "propertyId", title: "Airport" },
    { id: "firstName", title: "Firstname" },
    { id: "lastName", title: "Lastname" },
    { id: "email", title: "Email" },
    { id: "mobile", title: "Mobile" },
    // { id: "employmentInterests", title: "Employment Interests" },
    { id: "ei/foodAndBeverage", title: "Food & Beverage" },
    { id: "ei/retail", title: "Retail" },
    { id: "ei/construction", title: "Construction" },
    { id: "ei/maintenance", title: "Maintenance" },
    { id: "ei/professionalServices", title: "Professional Services" },
    {
      id: "airportEmploymentExperience",
      title: "Airport Employment Experience",
    },
    { id: "homeAddress1", title: "Home Address 1" },
    { id: "homeAddress2", title: "Home Address 2" },
    { id: "city", title: "City" },
    { id: "state", title: "State" },
    { id: "zipCode", title: "ZipCode" },
    { id: "advancedNetworkNews", title: "Advanced Network News" },
    { id: "emailUpdates", title: "Email Updates" },
    { id: "textUpdates", title: "Text Updates" },
    { id: "termsAndConditions", title: "Terms And Conditions" },
    { id: "locale", title: "Locale" },
  ],
  //   filter: "ALL",
  //   categoryLength: 5,
  // filter: (r) => r["ei/foodAndBeverage"] && r["ei/retail"] && r["ei/construction"] && r["ei/maintenance"] && r["ei/professionalServices"],
  form: FORM_TYPES.EMPLOYMENT_OPPORTUNITY,
  email: process.env.MARKETING_EMAIL,
  subject: "JFKT8 - Employment Opportunities",
};

const EMPLOYMENT_OPPORTUNITY_FILE_CP = {
  fileName: "employment-inquiries-cp",
  fields: EMPLOYMENT_OPPORTUNITY_FILE.fields,
  filter: (r) => r["ei/foodAndBeverage"] || r["ei/retail"] || r["ei/professionalServices"],
  //   filter: ["Food & Beverage", "Retail", "Professional Services"],
  form: FORM_TYPES.EMPLOYMENT_OPPORTUNITY,
  email: process.env.CP_EMAIL,
  subject: "Employment Opportunities",
};

const EMPLOYMENT_OPPORTUNITY_FILE_OPS = {
  fileName: "employment-inquiries-ops",
  fields: EMPLOYMENT_OPPORTUNITY_FILE.fields,
  filter: (r) => r["ei/maintenance"],
  //   filter: ["Maintenance"],
  form: FORM_TYPES.EMPLOYMENT_OPPORTUNITY,
  email: process.env.OPS_EMAIL,
  subject: "Employment Opportunities",
};

const EMPLOYMENT_OPPORTUNITY_FILE_HOLT = {
  fileName: "employment-inquiries-holt",
  fields: EMPLOYMENT_OPPORTUNITY_FILE.fields,
  //   filter: ["Construction"],
  filter: (r) => r["ei/construction"],
  form: FORM_TYPES.EMPLOYMENT_OPPORTUNITY,
  email: process.env.HOLT_EMAIL,
  subject: "Employment Opportunities",
};

const MY_T8_FILE = {
  fileName: "my-t8",
  fields: [
    { id: "createdAt", title: "Date" }, // Don't change the order of this field
    { id: "propertyId", title: "Airport" },
    { id: "firstName", title: "Firstname" },
    { id: "lastName", title: "Lastname" },
    { id: "email", title: "Email" },
    { id: "mobileNumber", title: "Mobile" },
    { id: "zipCode", title: "Zip Code" },
    { id: "airportBadgeHolder", title: "Airport Badge Holder" },
    { id: "whatTerminalsDoYouWorkIn", title: "Terminal" },
    { id: "currentRole", title: "Current Role" },
    { id: "interestedInOpportunities", title: "Interested Opportunities" },
    { id: "locale", title: "Locale" },
  ],
  form: FORM_TYPES.MY_T8,
  email: process.env.MARKETING_EMAIL,
  subject: "My T8 inquiries",
};

// const FILES = [
//   CONTACT_US_FILE,
//   STAY_UP_TO_DATE_FILE,
//   BUSINESS_OPPORTUNITY_FILE,
//   BUSINESS_OPPORTUNITY_FILE_HOLT,
//   BUSINESS_OPPORTUNITY_FILE_LEASING,
//   BUSINESS_OPPORTUNITY_FILE_OPS,
//   EMPLOYMENT_OPPORTUNITY_FILE,
//   EMPLOYMENT_OPPORTUNITY_FILE_CP,
//   EMPLOYMENT_OPPORTUNITY_FILE_OPS,
//   EMPLOYMENT_OPPORTUNITY_FILE_HOLT,
//   MY_T8_FILE,
// ];

const FILES = [
  CONTACT_US_FILE,
  // STAY_UP_TO_DATE_FILE,
  BUSINESS_OPPORTUNITY_FILE,
  // BUSINESS_OPPORTUNITY_FILE_HOLT,
  // BUSINESS_OPPORTUNITY_FILE_LEASING,
  // BUSINESS_OPPORTUNITY_FILE_OPS,
  EMPLOYMENT_OPPORTUNITY_FILE,
  // EMPLOYMENT_OPPORTUNITY_FILE_CP,
  // EMPLOYMENT_OPPORTUNITY_FILE_OPS,
  // EMPLOYMENT_OPPORTUNITY_FILE_HOLT,
  // MY_T8_FILE,
];

const BUSINESS_INTEREST_OPTIONS = [
  {
    key: "bi/architectureDesignEngineering",
    value: "Architecture/Design Engineering",
  },
  { key: "bi/construction", value: "Construction" },
  { key: "bi/foodAndBeverage", value: "Food & Beverage" },
  { key: "bi/productManufacturer", value: "Product Manufacturer/Maker" },
  {
    key: "bi/retailBusinessBrickMortar",
    value: "Retail Business (Brick & Mortar)",
  },
  { key: "bi/retailBusinessOnline", value: "Retail Business (Online)" },
  { key: "bi/professionalServices", value: "Professional Services" },
  { key: "bi/supplier", value: "Supplier" },
  { key: "bi/other", value: "Other" },
];

const CERTIFICATIONS_OPTIONS = [
  { key: "cert/acdbe", value: "ACDBE - Airport Concessions Disadvantaged Business Enterprise" },
  { key: "cert/lbe", value: "LBE - Local Business Enterprise" },
  { key: "cert/mbe", value: "MBE - Minority Business Enterprise" },
  { key: "cert/sdvosb", value: "SDVOSB - Service-Disabled Veteran-Owned Small Businesses" },
  { key: "cert/wbe", value: "WBE - Women Business Enterprise" },
  { key: "cert/sbe", value: "SBE - Small Business Enterprise" },
  { key: "cert/notCertified", value: "Not Certified" },
  { key: "cert/other", value: "Other" },
];

const EMPLOYMENT_INTEREST_OPTIONS = [
  { key: "ei/foodAndBeverage", value: "Food & Beverage" },
  { key: "ei/retail", value: "Retail" },
  { key: "ei/construction", value: "Construction" },
  { key: "ei/maintenance", value: "Maintenance" },
  { key: "ei/professionalServices", value: "Professional Services" },
];

const STAY_UP_TO_DATE_INTEREST_OPTIONS = [
  {
    key: "stayuptodate/businessOpportunities",
    value: "Business Opportunities",
  },
  { key: "stayuptodate/employmentOpportunities", value: "Employment Opportunities" },
  { key: "stayuptodate/general", value: "General" },
];
module.exports = {
  DYNAMO_TABLES,
  FORM_TYPES,
  FILES,
  BUSINESS_INTEREST_OPTIONS,
  EMPLOYMENT_INTEREST_OPTIONS,
  CERTIFICATIONS_OPTIONS,
  STAY_UP_TO_DATE_INTEREST_OPTIONS,
  JFKT8_EMAIL_TEMPLATES,
};
