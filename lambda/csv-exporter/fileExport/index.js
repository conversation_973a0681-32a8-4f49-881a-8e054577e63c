const AWS = require("aws-sdk");
const fs = require("fs");
const csv = require("csv-parser");
const createCsvWriter = require("csv-writer").createObjectCsvWriter;
const { GetObjectCommand, S3Client } = require("@aws-sdk/client-s3");
const { getSignedUrl } = require("@aws-sdk/s3-request-presigner");
const S3 = new AWS.S3();
const filePath = process.env.CSV_FILE_PATH;

const exportToCsvAndS3 = async (records, fileConfig) => {
  try {
    const fileName = getFileName(fileConfig.fileName);
    console.log(`Starting CSV export for ${fileName}`);
    console.log(`Initial records count: ${records.Items ? records.Items.length : 0}`);

    // Check if we have any records to process
    if (!records.Items || records.Items.length === 0) {
      console.log(`No records found for ${fileConfig.fileName}, skipping CSV generation`);
      return null; // Return null to indicate no file was created
    }

    let csvData = records.Items.map((item) => {
      console.log(JSON.stringify(item));
      const itemData = {};
      // console.log(JSON.stringify(item));
      for (const key in item) {
        if (fileConfig.fields.filter((m) => m.id == key).length > 0) {
          itemData[key] = `"${item[key]}"`; // Assuming all attributes are strings
        }
      }
      return itemData;
    });

    if (fileConfig.filter) {
      csvData = csvData.filter((r) => fileConfig.filter(r));
      console.log(`Records after filtering: ${csvData.length}`);
    }

    // Check again after filtering
    if (csvData.length === 0) {
      console.log(`No records remaining after filtering for ${fileConfig.fileName}, skipping CSV generation`);
      return null; // Return null to indicate no file was created
    }

    const csvString = convertToCSV(csvData, fileConfig);
    console.log(`CSV string generated successfully, length: ${csvString.length}`);

    // const csvWriter = createCsvWriter({
    //   path: `${filePath}/${fileName}`,
    //   header: fileConfig.fields
    // });

    // const csvData = csvjson.toCSV(filteredRecords, { headers: fileConfig.fields });

    // await csvWriter.writeRecords(filteredRecords || []);

    // Upload to S3 bucket
    // const data = await fs.readFileSync(`${filePath}/${fileName}`);

    await S3.upload({
      Bucket: process.env.S3_BUCKET_NAME,
      Key: fileName,
      Body: csvString,
      ContentType: "text/csv",
    }).promise();

    console.log(`Successfully uploaded ${fileName} to S3`);

    let presignedUrl = "";

    // try {
    //   presignedUrl = await S3.getSignedUrl("getObject", {
    //     Bucket: process.env.S3_BUCKET_NAME,
    //     Key: `${fileName}`,
    //     Expires: parseInt(process.env.PRESIGNED_URL_EXPIRATION), // Set the expiration time for the URL in seconds
    //   });
    // } catch (error) {
    //   console.log(JSON.stringify(error));
    // }

    try {
      const maxExpireTime = process.env.PRESIGNED_URL_EXPIRATION;
      const region = "us-west-2";

      const accessKeyId = process.env.S3_PRESIGNED_URL_ACCESS_KEY_ID;
      const secretAccessKey = process.env.S3_PRESIGNED_URL_SECRET_ACCESS_KEY;

      const s3client = new S3Client({
        region,
        credentials: {
          accessKeyId,
          secretAccessKey,
        },
        signatureVersion: "v4",
      });

      const getObjectCommand = new GetObjectCommand({
        Bucket: process.env.S3_BUCKET_NAME,
        Key: `${fileName}`,
      });
      presignedUrl = await getSignedUrl(s3client, getObjectCommand, {
        expiresIn: maxExpireTime,
      });
    } catch (error) {
      console.log(JSON.stringify(error));
    }

    return {
      // fileLink: result.Location,
      fileLink: presignedUrl,
      email: fileConfig.email,
      subject: fileConfig.subject,
    };
  } catch (error) {
    console.error(`Error in exportToCsvAndS3 for ${fileConfig.fileName}:`, JSON.stringify(error));
    throw error; // Re-throw to let the caller handle it
  }
};

const preferredOrder = (obj, order) => {
  var newObject = {};
  for (var i = 0; i < order.length; i++) {
    if (obj.hasOwnProperty(order[i])) {
      newObject[order[i]] = obj[order[i]];
    }
  }
  return newObject;
};

const getExistingRecords = async (fileConfig) => {
  const fileName = `${fileConfig.fileName}.csv`;
  const data = [];
  return new Promise((resolve, reject) => {
    fs.createReadStream(`${filePath}/${fileName}`)
      .pipe(csv())
      .on("data", function (row) {
        data.push(row);
      })
      .on("end", function () {
        resolve(data);
      })
      .on("error", function (error) {
        reject(error);
      });
  });
};

function convertToCSV(data, fileConfig) {
  const sortOrder = fileConfig.fields.map((x) => x.id);

  let newData = data.map((row) => {
    return preferredOrder(row, sortOrder);
  });

  // Handle empty data case
  if (!newData || newData.length === 0) {
    console.log("No data to convert to CSV, creating header-only CSV");
    // Create header-only CSV
    const header = fileConfig.fields.map((field) => field.title).join(",") + "\n";
    return header;
  }

  // console.log(JSON.stringify(data[0]));

  const header =
    Object.keys(newData[0])
      .map((item) => fileConfig.fields.filter((x) => x.id == item)[0].title)
      .join(",") + "\n";
  const rows = newData.map((item) => Object.values(item).join(",")).join("\n");
  return header + rows;
}

const downloadFromS3 = async (fileConfig) => {
  // const fileName = `${fileConfig.fileName}.csv`;
  const fileName = getFileName(fileConfig.fileName);

  const writeStream = fs.createWriteStream(`${filePath}/${fileName}`, {
    encoding: "utf-8",
    flags: "a",
  });

  const params = {
    Bucket: process.env.S3_BUCKET_NAME,
    Key: fileName,
  };

  return new Promise((resolve, reject) => {
    const readStream = S3.getObject(params).createReadStream();

    // Error handling in read stream
    readStream.on("error", async (e) => {
      console.log(e.code);
      if (e.code === "NoSuchKey") {
        // If file not found create a blank file with headers
        const csvWriter = createCsvWriter({
          path: `${filePath}/${fileName}`,
          header: fileConfig.fields,
        });

        await csvWriter.writeRecords([]);
        resolve(fileName);
      }
      reject(e);
    });

    // Resolve only if we are done writing
    writeStream.once("finish", () => {
      console.log("Finished writing", fileName);
      resolve(fileName);
    });

    // pipe will automatically finish the write stream once done
    readStream.pipe(writeStream);
  });
};

const getFormattedDate = () => {
  const date = new Date();
  const formattedDate = date
    .toLocaleDateString("en-GB", {
      day: "numeric",
      month: "short",
      year: "numeric",
    })
    .replace(/ /g, "-");

  return formattedDate;
};

const getFileName = (fileName) => {
  return `${fileName + "-" + getFormattedDate()}.csv`;
};

module.exports = { exportToCsvAndS3, downloadFromS3, getExistingRecords };
