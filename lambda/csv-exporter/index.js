const { getRows } = require("../dynamo");
const { exportToCsvAndS3, downloadFromS3, getExistingRecords } = require("./fileExport");
const { sendEmail } = require("./ses");
const { DYNAMO_TABLES, FORM_TYPES, FILES } = require("../constants");

exports.handler = async () => {
  try {
    // Get data from dynamoDB
    console.log("Fetching data from DynamoDB ... ");
    const promises = [
      getRows(DYNAMO_TABLES.CONTACT_RESPONSE),
      getRows(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, FORM_TYPES.EMPLOYMENT_OPPORTUNITY),
      getRows(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, FORM_TYPES.BUSINESS_OPPORTUNITY),
      getRows(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, FORM_TYPES.STAY_UP_TO_DATE),
      getRows(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, FORM_TYPES.MY_T8),
    ];

    const [contactUs, employeement, business, stayUpToDate, myT8] = await Promise.all(promises);
    // Get existing files from S3
    console.log("Downloading existing files from S3 ...");
    // const downloads = FILES.map((file) => downloadFromS3(file));

    // await Promise.all(downloads);

    // const existingCSVRecords = FILES.map((file) => getExistingRecords(file));

    // const [
    //   existingContactUs,
    //   // existingStayUptoDate,
    //   existingBusiness,
    //   // existingBusinessHolt,
    //   // existingBusinessLeasing,
    //   // existingBusinessOps,
    //   existingEmployment,
    //   // existingEmploymentCP,
    //   // existingEmploymentOps,
    //   // existingEmploymentHolt,
    //   // existingMyT8,
    // ] = await Promise.all(existingCSVRecords);

    // console.log(JSON.stringify(contactUs.Items))

    const contactUsRecords = contactUs.Items;
    const businessOpportunitiesToWrite = business.Items;
    const employmentOpportunitiesToWrite = employeement.Items;

    // const contactUsRecords = getContactUsRecordsToWrite(contactUs.Items, existingContactUs);
    // const stayUpToDateRecords = getStayUpToDateRecordsToWrite(stayUpToDate.Items, existingStayUptoDate);

    // const businessOpportunitiesToWrite = getBusinessOpportunitiesRecordsToWrite(business.Items, existingBusiness);
    // const businessOpportunitiesHoltToWrite = getBusinessOpportunitiesRecordsToWrite(business.Items, existingBusinessHolt);
    // const businessOpportunitiesLeasingToWrite = getBusinessOpportunitiesRecordsToWrite(business.Items, existingBusinessLeasing);
    // const businessOpportunitiesOpsToWrite = getBusinessOpportunitiesRecordsToWrite(business.Items, existingBusinessOps);

    // const employmentOpportunitiesToWrite = getEmploymentOpportunitiesRecordsToWrite(employeement.Items, existingEmployment);

    // const employmentOpportunitiesCPToWrite = getEmploymentOpportunitiesRecordsToWrite(employeement.Items, existingEmploymentCP);
    // const employmentOpportunitiesOpsToWrite = getEmploymentOpportunitiesRecordsToWrite(employeement.Items, existingEmploymentOps);
    // const employmentOpportunitiesHoltToWrite = getEmploymentOpportunitiesRecordsToWrite(employeement.Items, existingEmploymentHolt);

    // const myT8Records = getMyT8RecordsToWrite(myT8.Items, existingMyT8);

    console.log("Saving new data to downloaded files ... ");
    // Save new data to downloaded files and upload back to S3
    const exports = FILES.map((file) => {
      switch (file.form) {
        case FORM_TYPES.CONTACT_US:
          if (contactUs.Items.length > 0) {
            return exportToCsvAndS3(contactUs, file);
          } else {
            return null;
          }
        // case FORM_TYPES.STAY_UP_TO_DATE:
        //   return exportToCsvAndS3(stayUpToDateRecords, file);
        // case FORM_TYPES.MY_T8:
        //   return exportToCsvAndS3(myT8Records, file);
        case FORM_TYPES.BUSINESS_OPPORTUNITY:
          if (business.Items.length > 0) {
            return exportToCsvAndS3(business, file);
          } else {
            return null;
          }
        case FORM_TYPES.EMPLOYMENT_OPPORTUNITY:
          if (employeement.Items.length > 0) {
            return exportToCsvAndS3(employeement, file);
          } else {
            return null;
          }
      }
    });

    const results = await Promise.all(exports.filter((x) => x != null));

    // console.log(results)

    const emails = results.map((r) => sendEmail(r));
    // console.log(emails);
    const emailResult = await Promise.all(emails);

    return {
      statusCode: 200,
      body: JSON.stringify({ emailResult, results }),
    };
  } catch (error) {
    console.log(JSON.stringify(error));
    return {
      statusCode: 400,
      body: JSON.stringify(error),
    };
  }
};

const getContactUsRecordsToWrite = (records, existingRecords) => {
  let existingContactUsRecords = existingRecords
    .filter((item) => item.Firstname != "")
    .map((item) => {
      return {
        company: item.Company,
        createdAt: item.Date,
        email: item.Email,
        inquiryType: item["Inquiry Type"] || "",
        firstName: item.Firstname,
        lastName: item.Lastname,
        message: item.Message,
        mobile: item.Mobile,
        propertyId: item.Airport,
        locale: item.Locale,
      };
    });

  const recordsToWrite = [...records, ...existingContactUsRecords].sort((a, b) => {
    return new Date(b.createdAt) - new Date(a.createdAt);
  });

  return recordsToWrite;
};

const getStayUpToDateRecordsToWrite = (records, existingRecords) => {
  let existingStayUpToDateRecords = existingRecords
    .filter((item) => item.Mobile != "")
    .map((item) => {
      return {
        advancedNetworkNews: item["Advanced Network News"],
        createdAt: item.Date,
        email: item.Email,
        emailUpdates: item["Email Updates"],
        mobile: item.Mobile,
        propertyId: item.Airport,
        responseId: item.ResponseId,
        "stayuptodate/businessOpportunities": item["Business Opportunities"],
        "stayuptodate/employmentOpportunities": item["Employment Opportunities"],
        "stayuptodate/general": item["General"],
        termsAndConditions: item["Terms And Conditions"],
        textUpdates: item["Text Updates"],
        zipCode: item.ZipCode,
        locale: item.Locale,
      };
    });

  const recordsToWrite = [...records, ...existingStayUpToDateRecords].sort((a, b) => {
    return new Date(b.createdAt) - new Date(a.createdAt);
  });

  return recordsToWrite;
};

const getBusinessOpportunitiesRecordsToWrite = (records, existingRecords) => {
  let existingBusinessOpportunitiesRecords = existingRecords
    .filter((item) => item.Firstname != "")
    .map((item) => {
      return {
        aboutBusiness: item["About Business"],
        advancedNetworkNews: item["Advanced Network News"],
        airportExperience: item["Airport Experience"],
        "bi/architectureDesignEngineering": item["Architecture/Design Engineering"],
        "bi/construction": item["Construction"],
        "bi/foodAndBeverage": item["Food & Beverage"],
        "bi/other": item["Other"],
        "bi/productManufacturer": item["Product Manufacturer/Maker"],
        "bi/retailBusinessBrickMortar": item["Retail Business (Brick & Mortar)"],
        "bi/retailBusinessOnline": item["Retail Business (Online)"],
        "bi/professionalServices": item["Professional Services"],
        "bi/supplier": item["Supplier"],
        businessAddress1: item["Business Address 1"],
        businessAddress2: item["Business Address 2"],
        "cert/acdbe": item["ACDBE - Airport Concessions Disadvantaged Business Enterprise"],
        "cert/lbe": item["LBE - Local Business Enterprise"],
        "cert/mbe": item["MBE - Minority Business Enterprise"],
        "cert/sdvosb": item["SDVOSB - Service-Disabled Veteran-Owned Small Businesses"],
        "cert/wbe": item["WBE - Women Business Enterprise"],
        "cert/sbe": item["SBE - Small Business Enterprise"],
        "cert/notCertified": item["Not Certified"],
        "cert/other": item["Other"],
        city: item.City,
        companyName: item["Company Name"],
        createdAt: item.Date,
        email: item.Email,
        emailUpdates: item["Email Updates"],
        firstName: item.Firstname,
        lastName: item.Lastname,
        mobile: item.Mobile,
        propertyId: item.Airport,
        responseId: item.ResponseId,
        state: item.State,
        termsAndConditions: item["Terms And Conditions"],
        textUpdates: item["Text Updates"],
        website: item.Website,
        yearsInBusiness: item["Years In Business"],
        zipCode: item.ZipCode,
        locale: item.Locale,
      };
    });

  const recordsToWrite = [...records, ...existingBusinessOpportunitiesRecords].sort((a, b) => {
    return new Date(b.createdAt) - new Date(a.createdAt);
  });

  return recordsToWrite;
};

const getEmploymentOpportunitiesRecordsToWrite = (records, existingRecords) => {
  let existingEmploymentOpportunitiesRecords = existingRecords
    .filter((item) => item.Firstname != "")
    .map((item) => {
      return {
        advancedNetworkNews: item["Advanced Network News"],
        airportEmploymentExperience: item["Airport Employment Experience"],
        city: item["City"],
        createdAt: item["Date"],
        "ei/construction": item["Construction"],
        "ei/foodAndBeverage": item["Food & Beverage"],
        "ei/maintenance": item["Maintenance"],
        "ei/professionalServices": item["Professional Services"],
        "ei/retail": item["Retail"],
        email: item["Email"],
        emailUpdates: item["Email Updates"],
        firstName: item["Firstname"],
        homeAddress1: item["Home Address 1"],
        homeAddress2: item["Home Address 2"],
        lastName: item["Lastname"],
        mobile: item["Mobile"],
        propertyId: item["Airport"],
        responseId: item["ResponseId"],
        state: item["State"],
        termsAndConditions: item["Terms And Conditions"],
        textUpdates: item["Text Updates"],
        zipCode: item["ZipCode"],
        locale: item["Locale"],
      };
    });

  const recordsToWrite = [...records, ...existingEmploymentOpportunitiesRecords].sort((a, b) => {
    return new Date(b.createdAt) - new Date(a.createdAt);
  });

  return recordsToWrite;
};

const getMyT8RecordsToWrite = (records, existingRecords) => {
  let existingMyT8Records = existingRecords
    .filter((item) => item.Firstname != "")
    .map((item) => {
      return {
        firstName: item.Firstname,
        lastName: item.Lastname,
        email: item.Email,
        mobile: item.Mobile,
        createdAt: item["Date"],
        zipCode: item["Zip Code"],
        airportBadgeHolder: item["Airport Badge Holder"],
        whatTerminalsDoYouWorkIn: item.Terminal,
        currentRole: item["Current Role"],
        interestedInOpportunities: item["Interested Opportunities"],
        propertyId: item["Airport"],
        responseId: item["ResponseId"],
        locale: item.Locale,
      };
    });

  const recordsToWrite = [...records, ...existingMyT8Records].sort((a, b) => {
    return new Date(b.createdAt) - new Date(a.createdAt);
  });

  return recordsToWrite;
};
