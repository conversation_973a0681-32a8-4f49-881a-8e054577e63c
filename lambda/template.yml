AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: URW Airports contact forms handler

Resources:
  ApiGatewayApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: test
      Cors:
        AllowMethods: "'POST, GET, OPTIONS'"
        AllowHeaders: "'*'"
        AllowOrigin: "'*'"
        MaxAge: "'600'"
        AllowCredentials: true
  ApiFunction:
    Type: "AWS::Serverless::Function"
    Properties:
      Events:
        ApiEvent:
          Type: Api
          Properties:
            Path: /contactus
            Method: ANY
            Auth:
              Authorizer: NONE
      Handler: contact-form/index.handler
      Runtime: nodejs22.x
      Timeout: 300
      Environment:
        Variables:
          PROPERTY_ID: "JFK-T8"
          CONTACT_RESPONSE_DYNAMODB_TABLE_NAME: "urw-airports-demo-contact-response"
          OPPORTUNITY_RESPONSE_DYNAMODB_TABLE_NAME: "urw-airports-demo-opportunity-response"
          MAILCHIMP_API_KEY: *************************************
          MAILCHIMP_API_SERVER: us15
          MAILCHIMP_AUDIENCE_ID: 718a6cfd39
          CONTENTFUL_SPACE_ID: 76mm8bevcsv3
          CONTENTFUL_ENVIRONMENT: demo
          CONTENTFUL_ACCESS_TOKEN: S9w0jBRIbWaFcCn8bVJwqGtTI0V_09A2fYKHUAs-csM
          SITE_URL: "https://demo.thenewjfkt8.com"
          MAILCHIMP_TRANSACTIONAL_FROM_EMAIL : "<EMAIL>"
          MAILCHIMP_TRANSACTIONAL_API_KEY : "md-g56ItTTjBDWKAEDBmrUqmw"
          SOURCE_EMAIL: "<EMAIL>"
          FORMS_INTAKE_EMAIL: "<EMAIL>"
          JFKT8CONNECT_EMAIL : "<EMAIL>"
          CONTACT_MEDIA_INQUIRY_EMAIL: "<EMAIL>"
          NODE_ENV: "local" # Only for local development
  CSVExport:
    Type: "AWS::Serverless::Function"
    Properties:
      Events:
        ApiEvent:
          Type: Api
          Properties:
            Path: /csv
            Method: ANY
            Auth:
              Authorizer: NONE
      Handler: csv-exporter/index.handler
      Runtime: nodejs22.x
      Timeout: 300
      Environment:
        Variables:
          PROPERTY_ID: "JFK-T8"
          CSV_FILE_PATH: "/var/tmp"
          S3_BUCKET_NAME: "urw-airports-jfk-t8-demo-contactus-responses"
          PRESIGNED_URL_EXPIRATION: 3600
          CONTACT_RESPONSE_DYNAMODB_TABLE_NAME: "urw-airports-demo-contact-response"
          OPPORTUNITY_RESPONSE_DYNAMODB_TABLE_NAME: "urw-airports-demo-opportunity-response"
          HOLT_EMAIL: "<EMAIL>"
          MARKETING_EMAIL: "<EMAIL>"
          LEASING_EMAIL: "<EMAIL>"
          OPS_EMAIL: "<EMAIL>"
          CP_EMAIL: "<EMAIL>"
          SOURCE_EMAIL: "<EMAIL>"
          # FORMS_INTAKE_EMAIL: "<EMAIL>"
          CC_EMAILS: "<EMAIL>"
          S3_PRESIGNED_URL_ACCESS_KEY_ID: ********************
          S3_PRESIGNED_URL_SECRET_ACCESS_KEY : i+xD1jl1v5SrwJ09OPJ5n7GG6ePF7AZyF+WFpSd4
